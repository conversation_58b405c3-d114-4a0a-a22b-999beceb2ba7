@echo off
echo TCGA Radiology Batch Processor
echo ================================
echo.
echo This tool processes multiple TCGA radiology datasets from directories
echo ending with "-Opened" (like BLCA-Opened, BRCA-Opened, etc.)
echo.
echo Usage examples:
echo   tcga_batch_processor.exe                           (process current directory)
echo   tcga_batch_processor.exe "C:\Data\TCGA"           (process specific directory)
echo   tcga_batch_processor.exe -o "C:\Results"          (custom output directory)
echo   tcga_batch_processor.exe --help                    (show all options)
echo.
echo The processor will automatically find all XXXX-Opened directories
echo and process them using the TCGA radiology preprocessor.
echo.
pause
