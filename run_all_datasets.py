#!/usr/bin/env python3
"""
TCGA Radiology - Process All Datasets

This script automatically finds and processes all XXXX-Opened directories
in the current location using the TCGA radiology preprocessor.

Usage:
    python run_all_datasets.py

The script will:
1. Find all directories ending with "-Opened"
2. Process each one using the TCGA radiology preprocessor
3. Create individual output folders for each dataset
4. Generate a consolidated summary report

Author: AI Assistant
Date: 2025-08-30
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Import the existing preprocessor
try:
    from tcga_radiology_preprocessor import TCGARadiologyPreprocessor
except ImportError:
    print("Error: tcga_radiology_preprocessor.py not found in the current directory")
    print("Please ensure the preprocessor script is in the same directory as this script")
    sys.exit(1)


def find_opened_directories(base_dir: Path) -> List[Path]:
    """Find all directories ending with '-Opened'."""
    opened_dirs = []
    for item in base_dir.iterdir():
        if item.is_dir() and item.name.endswith('-Opened'):
            opened_dirs.append(item)
    return sorted(opened_dirs)


def find_manifest_directory(opened_dir: Path) -> Path:
    """Find the manifest directory within an opened directory."""
    manifest_dirs = list(opened_dir.glob("manifest*"))
    if not manifest_dirs:
        raise FileNotFoundError(f"No manifest directory found in {opened_dir}")
    return manifest_dirs[0]


def process_single_dataset(opened_dir: Path, base_output_dir: Path) -> Dict[str, Any]:
    """Process a single XXXX-Opened dataset."""
    dataset_name = opened_dir.name
    print(f"\n{'='*60}")
    print(f"PROCESSING: {dataset_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    result = {
        'dataset': dataset_name,
        'status': 'failed',
        'start_time': datetime.now().isoformat(),
        'input_dir': str(opened_dir),
        'output_dir': None,
        'error': None,
        'processing_time': 0,
        'patients_processed': 0,
        'total_patches': 0
    }
    
    try:
        # Find manifest directory
        manifest_dir = find_manifest_directory(opened_dir)
        print(f"Found manifest: {manifest_dir.name}")
        
        # Set up output directory
        output_dir = base_output_dir / f"{dataset_name}_processed"
        output_dir.mkdir(exist_ok=True)
        result['output_dir'] = str(output_dir)
        
        print(f"Input:  {manifest_dir}")
        print(f"Output: {output_dir}")
        
        # Initialize preprocessor
        preprocessor = TCGARadiologyPreprocessor(
            input_dir=str(manifest_dir),
            output_dir=str(output_dir),
            target_spacing=(1.0, 1.0, 1.0),  # 1mm isotropic
            target_size=None,  # Keep original size
            intensity_range=(0.0, 1.0),  # Normalize to [0, 1]
            patch_size=(64, 64, 64),  # 64x64x64 patches
            patch_overlap=0.25  # 25% overlap
        )
        
        # Run preprocessing
        print("Starting preprocessing...")
        summary = preprocessor.run_preprocessing()
        
        # Update result
        if summary['status'] == 'completed':
            result['status'] = 'success'
            result['patients_processed'] = summary['successful_patients']
            result['total_patches'] = summary['total_patches_extracted']
            print(f"✅ SUCCESS: {summary['successful_patients']} patients, {summary['total_patches_extracted']} patches")
        else:
            result['error'] = summary.get('error', 'Unknown preprocessing error')
            print(f"❌ FAILED: {result['error']}")
            
    except Exception as e:
        result['error'] = str(e)
        print(f"❌ ERROR: {e}")
    
    # Calculate processing time
    result['processing_time'] = time.time() - start_time
    result['end_time'] = datetime.now().isoformat()
    
    print(f"Processing time: {result['processing_time']:.1f} seconds")
    return result


def generate_summary_report(results: List[Dict[str, Any]], output_file: Path):
    """Generate a consolidated summary report."""
    
    # Calculate totals
    total_datasets = len(results)
    successful_datasets = sum(1 for r in results if r['status'] == 'success')
    total_patients = sum(r['patients_processed'] for r in results)
    total_patches = sum(r['total_patches'] for r in results)
    total_time = sum(r['processing_time'] for r in results)
    
    # Create summary
    summary = {
        'processing_date': datetime.now().isoformat(),
        'total_datasets': total_datasets,
        'successful_datasets': successful_datasets,
        'failed_datasets': total_datasets - successful_datasets,
        'total_patients_processed': total_patients,
        'total_patches_extracted': total_patches,
        'total_processing_time_seconds': total_time,
        'average_time_per_dataset': total_time / total_datasets if total_datasets > 0 else 0,
        'datasets': results
    }
    
    # Save to file
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    return summary


def main():
    """Main processing function."""
    print("TCGA Radiology - Batch Processing All Datasets")
    print("=" * 60)
    
    # Set up directories
    base_dir = Path.cwd()
    output_dir = base_dir / "processed_results"
    output_dir.mkdir(exist_ok=True)
    
    print(f"Base directory: {base_dir}")
    print(f"Output directory: {output_dir}")
    
    # Find all -Opened directories
    opened_dirs = find_opened_directories(base_dir)
    
    if not opened_dirs:
        print("❌ No directories ending with '-Opened' found!")
        print("Expected directories like: BLCA-Opened, BRCA-Opened, etc.")
        sys.exit(1)
    
    print(f"\nFound {len(opened_dirs)} datasets to process:")
    for i, dir_path in enumerate(opened_dirs, 1):
        print(f"  {i:2d}. {dir_path.name}")
    
    # Confirm processing
    print(f"\nThis will process all {len(opened_dirs)} datasets.")
    print("Each dataset may take several minutes to hours depending on size.")
    
    response = input("\nProceed with processing? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Processing cancelled.")
        sys.exit(0)
    
    # Process each dataset
    all_results = []
    start_time = time.time()
    
    for i, opened_dir in enumerate(opened_dirs, 1):
        print(f"\n🔄 DATASET {i}/{len(opened_dirs)}")
        try:
            result = process_single_dataset(opened_dir, output_dir)
            all_results.append(result)
        except KeyboardInterrupt:
            print("\n❌ Processing interrupted by user")
            break
        except Exception as e:
            print(f"❌ Unexpected error processing {opened_dir.name}: {e}")
            result = {
                'dataset': opened_dir.name,
                'status': 'failed',
                'error': str(e),
                'processing_time': 0
            }
            all_results.append(result)
    
    # Generate summary report
    total_time = time.time() - start_time
    summary_file = output_dir / "batch_processing_summary.json"
    summary = generate_summary_report(all_results, summary_file)
    
    # Print final summary
    print(f"\n{'='*60}")
    print("BATCH PROCESSING COMPLETE!")
    print(f"{'='*60}")
    print(f"📊 Processed: {summary['successful_datasets']}/{summary['total_datasets']} datasets")
    print(f"👥 Total patients: {summary['total_patients_processed']}")
    print(f"🧩 Total patches: {summary['total_patches_extracted']}")
    print(f"⏱️  Total time: {total_time/3600:.1f} hours")
    print(f"📁 Results saved to: {output_dir}")
    print(f"📋 Summary report: {summary_file}")
    
    # Show failed datasets
    failed_datasets = [r for r in all_results if r['status'] == 'failed']
    if failed_datasets:
        print(f"\n❌ Failed datasets ({len(failed_datasets)}):")
        for result in failed_datasets:
            print(f"   - {result['dataset']}: {result.get('error', 'Unknown error')}")
    
    print(f"\n🎉 Batch processing complete!")


if __name__ == "__main__":
    main()
