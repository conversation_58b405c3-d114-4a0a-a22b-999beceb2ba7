# TCGA Radiology Batch Processor - Standalone Executable

This is a standalone executable that processes multiple TCGA radiology datasets using the existing radiology preprocessor. It can run on any machine without requiring Python to be installed.

## What it does

The batch processor:
- Scans for directories ending with "-Opened" (like BLCA-Opened, BRCA-Opened, etc.)
- Processes each dataset using the TCGA radiology preprocessor
- Performs DICOM standardization, resampling, intensity normalization
- Extracts patches for AI training
- Generates quality control reports
- Creates consolidated batch processing reports

## Expected Directory Structure

```
Your-Data-Directory/
├── BLCA-Opened/
│   └── manifest-1567090213715/
│       └── TCGA-BLCA/
│           ├── TCGA-4Z-AA7M/
│           │   └── 03-06-2007-NA-TX AS AI-91720/
│           │       ├── 1.000000-SCOUT-38276/
│           │       ├── 2.000000-SC 2.5mm-08283/
│           │       └── ...
│           └── TCGA-4Z-AA7N/
├── BRCA-Opened/
│   └── manifest-25vRPwyh8987165612391086998/
│       └── TCGA-BRCA/
│           ├── TCGA-AO-A03M/
│           └── TCGA-AO-A03V/
└── Other-Cancer-Opened/
```

## Usage

### Basic usage (process current directory):
```
tcga_batch_processor.exe
```

### Process specific directory:
```
tcga_batch_processor.exe "C:\Path\To\Your\TCGA\Data"
```

### Custom output directory:
```
tcga_batch_processor.exe -o "C:\Results"
```

### Show all options:
```
tcga_batch_processor.exe --help
```

## Output

For each XXXX-Opened dataset, the processor creates:
- `processed/` - Standardized NIfTI images
- `patches/` - PyTorch tensor files with extracted patches
- `qc/` - Quality control reports in JSON format
- `logs/` - Processing logs

Additionally, a batch summary is created:
- `batch_processing_summary.json` - Overall processing results

## Features

- **Automatic Discovery**: Finds all directories ending with "-Opened"
- **Robust Processing**: Handles various DICOM formats and modalities
- **Quality Control**: Automated QC checks with detailed reporting
- **Batch Reporting**: Consolidated results across all datasets
- **Error Handling**: Graceful handling of corrupted or missing data
- **Progress Tracking**: Real-time progress updates and logging

## Requirements

- No Python installation required
- Works on Windows, macOS, and Linux
- Handles any number of XXXX-Opened directories
- Automatically creates metadata.csv files if missing

## Performance Notes

- Processing time depends on dataset size and number of series
- Large datasets may take several hours to process
- The processor limits patches per patient to manage memory usage
- Progress is logged in real-time to track processing status

## Troubleshooting

1. **No datasets found**: Ensure directories end with "-Opened"
2. **Processing fails**: Check logs in the output directory
3. **Memory issues**: Reduce patch size or limit concurrent processing
4. **DICOM errors**: Some corrupted files are automatically skipped

## Output Files

- `*.nii.gz` - Processed medical images in NIfTI format
- `*.pt` - PyTorch tensor files with image patches
- `*_qc.json` - Quality control metrics for each processed image
- `batch_processing_summary.json` - Overall batch processing results
- `*.log` - Detailed processing logs
